---
export interface Props {
	title: string;
	description: string;
	color?: "blue" | "green" | "purple" | "orange";
}

const { title, description, color = "blue" } = Astro.props;

const colorClasses = {
	blue: "bg-blue-600",
	green: "bg-green-600", 
	purple: "bg-purple-600",
	orange: "bg-orange-600"
};
---

<div class="text-center my-20">
	<div class={`inline-block ${colorClasses[color]} text-white px-8 py-4 rounded-full font-semibold text-xl`}>
		{title}
	</div>
	<p class="text-gray-600 mt-3 text-lg">{description}</p>
</div> 