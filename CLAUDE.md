# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Astro-based corporate website for 青釭金融科技 (Qing Gang Financial Technology), a Chinese AI financial technology company. The site showcases their AI products and services, focusing on privacy computing, intelligent decision-making, and B2C industry digital transformation.

## Commands

Run from the project root:

| Command | Description |
|---------|-------------|
| `pnpm install` | Install dependencies |
| `pnpm dev` | Start development server at localhost:4321 |
| `pnpm build` | Build production site to ./dist/ |
| `pnpm preview` | Preview build locally |
| `pnpm astro ...` | Run Astro CLI commands |

## Technology Stack

- **Framework**: Astro 5.11.1 (Static Site Generator)
- **Styling**: Tailwind CSS 4.1.11 (with @tailwindcss/vite)
- **Language**: TypeScript (strict mode)
- **Package Manager**: pnpm
- **Build Tool**: Vite (integrated with Astro)

## Architecture

### Layout System
- **Base Layout** (`src/layouts/Layout.astro`): Core HTML structure with meta tags, fonts, and CSS variables
- **Main Layout** (`src/layouts/MainLayout.astro`): Extends base layout with Header/Footer wrapper

### Component Structure
- **Header** (`src/components/Header.astro`): Navigation with dropdown menus, mobile responsive
- **Footer** (`src/components/Footer.astro`): Company info, links, social media
- **Timeline Components**: `TimelineItem.astro` and `TimelinePhase.astro` for displaying company history
- **Language Toggle** (`src/components/LanguageToggle.astro`): Internationalization support

### Page Organization
- **Home** (`src/pages/index.astro`): Company overview, mission, team introduction
- **Products** (`src/pages/products.astro`): Comprehensive product showcase with 3-layer architecture
- **Market** (`src/pages/market.astro`): Market analysis and case studies
- **About** (`src/pages/about.astro`): Company information and team details
- **Contact** (`src/pages/contact.astro`): Contact information and forms
- **FAQ** (`src/pages/faq.astro`): Frequently asked questions

## Design System

### Color Scheme (defined in Layout.astro)
- Primary Blue: `#0052CC`
- Secondary Gray: `#4A4A4A`
- Accent Green: `#00A86B`
- Highlight Orange: `#F28C38`
- Text Dark: `#1a1a1a`
- Text Light: `#6b7280`
- Background Light: `#f8fafc`

### Typography
- Primary Font: 'Noto Sans SC' (Chinese)
- Secondary Font: 'Inter' (English)
- Fallback: system-ui, sans-serif

### Responsive Design
- Mobile-first approach using Tailwind CSS
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Mobile navigation with collapsible dropdowns

## Content Structure

### Company Products (3-Layer Architecture)
1. **Application Layer**: Model.AI, Strategy.AI, Robot.AI, AR.Digital
2. **Platform Layer**: MXC.AI, Rochat.AI, FL.AI
3. **Infrastructure Layer**: Model training, Data encryption

### Key Features
- Multilingual support (Chinese Traditional)
- SEO optimization with meta descriptions
- Interactive components with vanilla JavaScript
- Privacy-focused AI technology presentation
- Professional corporate design

## Development Guidelines

### File Naming
- Use kebab-case for files and directories
- Astro components use PascalCase.astro
- TypeScript interfaces defined in component frontmatter

### Component Patterns
- Use Astro's component syntax with frontmatter for TypeScript
- Props interfaces defined at component level
- CSS-in-JS with style tags for component-specific styles
- Tailwind classes for utility styling

### JavaScript Integration
- Client-side scripts embedded in component files
- DOM manipulation for interactive features (mobile menu, dropdowns)
- TypeScript support for script sections

### Content Management
- Static content embedded in components
- Consistent company branding and messaging
- Professional Chinese business terminology

## Key Business Context

The website represents a Chinese AI financial technology company specializing in:
- Privacy computing + encrypted transmission (隱私計算+加密傳輸)
- AI + Big Data technology (AI+大數據)
- AI + Intelligent decision-making systems (AI+智能決策)
- AI + Intelligent generation services (AI+智能生成)
- B2C industry digital transformation services for finance, retail, education, and internet platforms

Understanding this context is crucial for maintaining appropriate tone and technical accuracy when making changes to the site.